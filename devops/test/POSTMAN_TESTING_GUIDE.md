# CRUD Master - Postman Testing Guide

## Overview

This guide provides comprehensive instructions for testing the CRUD Master microservices project using the provided Postman collection and environment files.

## Files Included

1. **CRUD_Master_Collection.postman_collection.json** - Complete API test collection
2. **CRUD_Master_Environment.postman_environment.json** - Environment variables
3. **POSTMAN_TESTING_GUIDE.md** - This documentation

## Quick Setup

### 1. Import Files into Postman

1. **Open Postman**
2. **Import Collection**:
   - Click "Import" button
   - Select `CRUD_Master_Collection.postman_collection.json`
   - Click "Import"

3. **Import Environment**:
   - Click "Import" button  
   - Select `CRUD_Master_Environment.postman_environment.json`
   - Click "Import"

4. **Activate Environment**:
   - Click environment dropdown (top right)
   - Select "CRUD Master Environment"

### 2. Verify System is Running

Before running tests, ensure all VMs are running:
```bash
cd /path/to/crud-master
vagrant status
# Should show all VMs as "running"
```

## Test Collection Structure

### 🎬 Movie Inventory API (11 requests)
1. **Get All Movies (Empty)** - Initial state verification
2. **Create Movie - The Matrix** - Create first test movie
3. **Create Movie - Inception** - Create second test movie  
4. **Get All Movies (With Data)** - Verify movies were created
5. **Get Movie by ID** - Retrieve specific movie
6. **Search Movies by Title** - Test search functionality
7. **Update Movie** - Modify existing movie
8. **Get Movie by Invalid ID (404 Test)** - Error handling
9. **Create Movie with Invalid Data (400 Test)** - Validation testing

### 💳 Billing API (2 requests)
1. **Create Billing Order** - Process payment order
2. **Create Multiple Billing Orders** - Test multiple orders

### 🗑️ Cleanup Operations (4 requests)
1. **Delete Specific Movie** - Remove single movie
2. **Verify Movie Deleted (404 Test)** - Confirm deletion
3. **Delete All Movies** - Clear all test data
4. **Verify All Movies Deleted** - Confirm cleanup

## Environment Variables

The environment includes these pre-configured variables:

| Variable | Value | Description |
|----------|-------|-------------|
| `base_url` | http://*************:3000 | API Gateway URL |
| `gateway_host` | ************* | Gateway VM IP |
| `gateway_port` | 3000 | Gateway port |
| `inventory_host` | ************* | Inventory VM IP |
| `inventory_port` | 8080 | Inventory port |
| `billing_host` | ************* | Billing VM IP |
| `movie_id` | (dynamic) | Stores created movie ID |
| `test_user_id` | test_user_123 | Sample user ID |
| `sample_movie_title` | The Matrix | Default movie title |
| `sample_movie_description` | A computer programmer... | Default description |

## Running Tests

### Option 1: Run Entire Collection
1. Click on "CRUD Master Microservices API Collection"
2. Click "Run" button
3. Select all requests or specific folders
4. Click "Run CRUD Master..."
5. View results in the runner

### Option 2: Run Individual Requests
1. Navigate to specific request
2. Click "Send" button
3. View response and test results

### Option 3: Run by Folder
1. Right-click on folder (e.g., "🎬 Movie Inventory API")
2. Select "Run folder"
3. View results

## Test Scenarios Covered

### ✅ Happy Path Testing
- Create movies with valid data
- Retrieve all movies
- Search movies by title
- Get specific movie by ID
- Update existing movie
- Delete specific movie
- Delete all movies
- Process billing orders

### ✅ Error Handling Testing
- Get non-existent movie (404)
- Create movie with invalid data (400/500)
- Verify deleted movie returns 404

### ✅ Data Validation Testing
- Response structure validation
- Data type verification
- Required field presence
- Response time validation

### ✅ Workflow Testing
- End-to-end movie lifecycle
- Multiple order processing
- Cleanup verification

## Expected Test Results

### Successful Run Metrics
- **Total Requests**: 17
- **Passed Tests**: ~60+ assertions
- **Failed Tests**: 0 (if system is healthy)
- **Average Response Time**: < 2000ms per request

### Key Assertions
- Status codes (200, 201, 204, 404)
- Response structure validation
- Data integrity checks
- Error message verification
- Response time limits

## Sample Test Data

### Movie Creation Payloads
```json
{
  "title": "The Matrix",
  "description": "A computer programmer discovers reality is a simulation"
}

{
  "title": "Inception", 
  "description": "A thief who enters people's dreams to steal secrets is given the task of planting an idea in someone's mind."
}
```

### Billing Order Payloads
```json
{
  "user_id": "test_user_123",
  "number_of_items": "3",
  "total_amount": "45.99"
}

{
  "user_id": "premium_user_456",
  "number_of_items": "10", 
  "total_amount": "199.99"
}
```

## Troubleshooting

### Common Issues

#### Connection Refused Errors
```
Error: connect ECONNREFUSED *************:3000
```
**Solution**: 
- Verify VMs are running: `vagrant status`
- Check services: `vagrant ssh gateway-vm -c "sudo pm2 list"`

#### 500 Internal Server Errors
**Solution**:
- Check service logs: `vagrant ssh inventory-vm -c "sudo pm2 logs inventory_app"`
- Restart services: `vagrant ssh inventory-vm -c "sudo pm2 restart inventory_app"`

#### Database Connection Errors
**Solution**:
- Check PostgreSQL: `vagrant ssh inventory-vm -c "sudo systemctl status postgresql"`
- Verify database exists: `vagrant ssh inventory-vm -c "sudo -u postgres psql -l"`

#### Test Failures
**Common Causes**:
- Services not fully started (wait 30 seconds after `vagrant up`)
- Database authentication issues
- Network connectivity problems
- Previous test data interference

### Debugging Steps

1. **Check VM Status**:
   ```bash
   vagrant status
   ```

2. **Check Service Health**:
   ```bash
   vagrant ssh gateway-vm -c "sudo pm2 list"
   vagrant ssh inventory-vm -c "sudo pm2 list"  
   vagrant ssh billing-vm -c "sudo pm2 list"
   ```

3. **Test Direct Connectivity**:
   ```bash
   curl http://*************:3000/api/movies
   ```

4. **View Service Logs**:
   ```bash
   vagrant ssh gateway-vm -c "sudo pm2 logs gateway --lines 20"
   ```

## Advanced Usage

### Custom Test Scenarios

You can modify the collection to add:
- Performance testing with larger datasets
- Concurrent request testing
- Extended error scenario testing
- Custom business logic validation

### Environment Customization

Create additional environments for:
- Local development testing
- Different VM configurations
- Load testing scenarios
- Production-like environments

### Automated Testing

Integrate with CI/CD pipelines using:
```bash
newman run CRUD_Master_Collection.postman_collection.json \
  -e CRUD_Master_Environment.postman_environment.json \
  --reporters cli,json
```

## Support

For issues with the Postman collection:
1. Verify system is running correctly
2. Check environment variables are set
3. Review test logs in Postman console
4. Refer to main USER_GUIDE.md for system troubleshooting

---

**Collection Version**: 1.0  
**Last Updated**: 2024-12-29  
**Compatible with**: Postman v10.0+
