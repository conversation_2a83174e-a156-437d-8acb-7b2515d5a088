# -*- mode: ruby -*-
# vi: set ft=ruby :

# Load environment variables from .env file
begin
  require 'dotenv'
  Dotenv.load
rescue LoadError
  # Fallback: manually load .env file
  if File.exist?('.env')
    File.readlines('.env').each do |line|
      key, value = line.strip.split('=', 2)
      ENV[key] = value if key && value
    end
  end
end

Vagrant.configure("2") do |config|
  config.vm.box = "ubuntu/focal64"

  # Gateway VM
  config.vm.define "gateway-vm" do |gateway|
    gateway.vm.hostname = "gateway-vm"
    gateway.vm.network "private_network", ip: ENV['GATEWAY_HOST']
    
    gateway.vm.provider "virtualbox" do |vb|
      vb.memory = "1024"
      vb.name = "gateway-vm"
    end
    
    gateway.vm.provision "shell", path: "scripts/setup-gateway.sh", env: {
      "GATEWAY_PORT" => ENV['GATEWAY_PORT'],
      "INVENTORY_API_HOST" => ENV['INVENTORY_API_HOST'],
      "INVENTORY_API_PORT" => ENV['INVENTORY_API_PORT'],
      "RABBITMQ_HOST" => ENV['RABBITMQ_HOST'],
      "RABBITMQ_PORT" => ENV['RABBITMQ_PORT'],
      "RABBITMQ_USER" => ENV['RABBITMQ_USER'],
      "RABBITMQ_PASSWORD" => ENV['RABBITMQ_PASSWORD'],
      "RABBITMQ_QUEUE" => ENV['RABBITMQ_QUEUE']
    }
  end

  # Inventory VM
  config.vm.define "inventory-vm" do |inventory|
    inventory.vm.hostname = "inventory-vm"
    inventory.vm.network "private_network", ip: ENV['INVENTORY_API_HOST']
    
    inventory.vm.provider "virtualbox" do |vb|
      vb.memory = "1024"
      vb.name = "inventory-vm"
    end
    
    inventory.vm.provision "shell", path: "scripts/setup-inventory.sh", env: {
      "INVENTORY_API_PORT" => ENV['INVENTORY_API_PORT'],
      "INVENTORY_DB_NAME" => ENV['INVENTORY_DB_NAME'],
      "INVENTORY_DB_USER" => ENV['INVENTORY_DB_USER'],
      "INVENTORY_DB_PASSWORD" => ENV['INVENTORY_DB_PASSWORD'],
      "INVENTORY_DB_HOST" => ENV['INVENTORY_DB_HOST']
    }
  end

  # Billing VM
  config.vm.define "billing-vm" do |billing|
    billing.vm.hostname = "billing-vm"
    billing.vm.network "private_network", ip: ENV['BILLING_API_HOST']
    
    billing.vm.provider "virtualbox" do |vb|
      vb.memory = "1024"
      vb.name = "billing-vm"
    end
    
    billing.vm.provision "shell", path: "scripts/setup-billing.sh", env: {
      "BILLING_API_PORT" => ENV['BILLING_API_PORT'],
      "BILLING_DB_NAME" => ENV['BILLING_DB_NAME'],
      "BILLING_DB_USER" => ENV['BILLING_DB_USER'],
      "BILLING_DB_PASSWORD" => ENV['BILLING_DB_PASSWORD'],
      "BILLING_DB_HOST" => ENV['BILLING_DB_HOST'],
      "RABBITMQ_USER" => ENV['RABBITMQ_USER'],
      "RABBITMQ_PASSWORD" => ENV['RABBITMQ_PASSWORD'],
      "RABBITMQ_QUEUE" => ENV['RABBITMQ_QUEUE']
    }
  end
end
