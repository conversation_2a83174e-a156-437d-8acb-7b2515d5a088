# CRUD Master - Quick Start Guide

## 🚀 Getting Started (After vagrant up)

### 1. Start All Services
After running `vagrant up`, the VMs are created but services need to be started manually:

```bash
# Option A: Use the automated script (recommended)
./start-services.sh

# Option B: Manual startup
vagrant ssh gateway-vm -c "cd /vagrant/srcs/api-gateway && sudo pm2 start ecosystem.config.js"
vagrant ssh inventory-vm -c "cd /vagrant/srcs/inventory-app && sudo pm2 start ecosystem.config.js"  
vagrant ssh billing-vm -c "cd /vagrant/srcs/billing-app && sudo pm2 start ecosystem.config.js"
```

### 2. Fix Database Authentication (if needed)
If you see database connection errors:

```bash
vagrant ssh inventory-vm -c "sudo -u postgres psql -c \"ALTER USER postgres PASSWORD 'postgres123';\""
vagrant ssh billing-vm -c "sudo -u postgres psql -c \"ALTER USER postgres PASSWORD 'postgres123';\""

# Restart services
vagrant ssh inventory-vm -c "sudo pm2 restart inventory_app"
vagrant ssh billing-vm -c "sudo pm2 restart billing_app"
```

### 3. Test API Connectivity

```bash
# Test via localhost (port forwarding)
curl http://localhost:3000/api/movies

# Test via VM IP
curl http://*************:3000/api/movies

# Both should return: []
```

## 📡 Available Endpoints

### Via Localhost (Port Forwarded)
- **API Gateway**: http://localhost:3000
- **Movies API**: http://localhost:3000/api/movies
- **Billing API**: http://localhost:3000/api/billing

### Via VM IP (Direct Access)
- **API Gateway**: http://*************:3000
- **Movies API**: http://*************:3000/api/movies
- **Billing API**: http://*************:3000/api/billing

## 🧪 Postman Testing

### 1. Import Files
- Import: `CRUD_Master_Collection.postman_collection.json`
- Import: `CRUD_Master_Environment.postman_environment.json`
- Select "CRUD Master Environment"

### 2. Environment Variables (Auto-Configured)
The collection automatically sets up these variables:
- `base_url`: http://localhost:3000 (uses port forwarding)
- `base_url_vm`: http://*************:3000 (direct VM access)
- `movie_id`: (dynamically set during tests)
- `test_user_id`: test_user_123

### 3. Run Tests
- **Full Suite**: Click collection → Run → Run all requests
- **Individual**: Select request → Send
- **By Section**: Right-click folder → Run folder

## 🔧 Troubleshooting

### Services Not Responding
```bash
# Check service status
vagrant ssh gateway-vm -c "sudo pm2 list"
vagrant ssh inventory-vm -c "sudo pm2 list"
vagrant ssh billing-vm -c "sudo pm2 list"

# Restart all services
./start-services.sh
```

### Database Connection Issues
```bash
# Check PostgreSQL status
vagrant ssh inventory-vm -c "sudo systemctl status postgresql"

# Reset database password
vagrant ssh inventory-vm -c "sudo -u postgres psql -c \"ALTER USER postgres PASSWORD 'postgres123';\""
```

### Port Forwarding Issues
```bash
# Check if port 3000 is forwarded
vagrant port gateway-vm

# Should show: 3000 (guest) => 3000 (host)
```

### VM Issues
```bash
# Check VM status
vagrant status

# Restart VMs if needed
vagrant reload

# Re-run startup script
./start-services.sh
```

## 📊 Service Status Check

### Quick Health Check
```bash
# Test all endpoints
curl -s http://localhost:3000/api/movies | jq .
curl -s -X POST http://localhost:3000/api/billing \
  -H "Content-Type: application/json" \
  -d '{"user_id":"test","number_of_items":"1","total_amount":"9.99"}' | jq .
```

### Detailed Status
```bash
# Check all PM2 processes
for vm in gateway-vm inventory-vm billing-vm; do
  echo "=== $vm ==="
  vagrant ssh $vm -c "sudo pm2 list"
  echo ""
done
```

## 🎯 Common Workflows

### Development Testing
1. Make code changes in `srcs/` directory
2. Restart affected service: `vagrant ssh <vm> -c "sudo pm2 restart <service>"`
3. Test with Postman or curl

### Full System Reset
```bash
# Stop all services
vagrant ssh gateway-vm -c "sudo pm2 stop all"
vagrant ssh inventory-vm -c "sudo pm2 stop all"
vagrant ssh billing-vm -c "sudo pm2 stop all"

# Clear databases (optional)
vagrant ssh inventory-vm -c "sudo -u postgres psql -d movies -c 'DELETE FROM movies;'"
vagrant ssh billing-vm -c "sudo -u postgres psql -d orders -c 'DELETE FROM orders;'"

# Restart services
./start-services.sh
```

### Performance Testing
```bash
# Test with multiple concurrent requests
for i in {1..10}; do
  curl -X POST http://localhost:3000/api/movies \
    -H "Content-Type: application/json" \
    -d "{\"title\":\"Movie $i\",\"description\":\"Test movie $i\"}" &
done
wait
```

## 📝 Notes

- **Port Forwarding**: Gateway VM port 3000 is forwarded to localhost:3000
- **Auto-Setup**: Postman collection automatically configures environment variables
- **Database**: PostgreSQL runs on inventory-vm and billing-vm
- **Message Queue**: RabbitMQ runs on billing-vm
- **Process Manager**: PM2 manages all application processes

## 🆘 Getting Help

1. **Check Logs**: `vagrant ssh <vm> -c "sudo pm2 logs <service>"`
2. **System Logs**: `vagrant ssh <vm> -c "sudo journalctl -f"`
3. **Database Logs**: `vagrant ssh <vm> -c "sudo tail -f /var/log/postgresql/*.log"`
4. **Run Startup Script**: `./start-services.sh` (shows detailed status)

---

**Quick Commands Reference:**
```bash
# Start everything
./start-services.sh

# Test API
curl http://localhost:3000/api/movies

# Check status
vagrant status && vagrant ssh gateway-vm -c "sudo pm2 list"

# Restart service
vagrant ssh inventory-vm -c "sudo pm2 restart inventory_app"
```
