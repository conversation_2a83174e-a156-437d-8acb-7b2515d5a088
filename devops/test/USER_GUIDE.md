# CRUD Master - User Guide

## Table of Contents
1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Quick Start](#quick-start)
4. [API Documentation](#api-documentation)
5. [Testing the APIs](#testing-the-apis)
6. [Managing Services](#managing-services)
7. [Database Access](#database-access)
8. [Troubleshooting](#troubleshooting)
9. [Development](#development)

## Overview

CRUD Master is a microservices-based movie streaming platform infrastructure that demonstrates modern distributed system architecture. The system consists of three main components:

- **API Gateway** (*************:3000): Routes requests to appropriate services
- **Inventory API** (*************:8080): Manages movie catalog with CRUD operations
- **Billing API** (*************): Processes payment orders via RabbitMQ messaging

### Architecture Highlights
- **Microservices**: Each service runs independently with its own database
- **Message Queue**: RabbitMQ for asynchronous billing processing
- **Load Balancing**: API Gateway distributes requests
- **Database**: PostgreSQL for data persistence
- **Process Management**: PM2 ensures service reliability
- **Virtualization**: Vagrant with VirtualBox for isolated environments

## Prerequisites

Before starting, ensure you have the following installed:

- **VirtualBox** (or VMware)
- **Vagrant** 
- **Ruby** with `dotenv` gem
- **Postman** (recommended for API testing)

### Installation Commands
```bash
# Install dotenv gem (if not already installed)
gem install dotenv

# Verify installations
vagrant --version
vboxmanage --version
```

## Quick Start

### 1. Clone and Setup
```bash
# Navigate to project directory
cd /path/to/crud-master

# Verify all files are present
ls -la
# Should see: .env, Vagrantfile, scripts/, srcs/, README.md
```

### 2. Start All Services
```bash
# Start all VMs (this will take 10-15 minutes)
vagrant up

# Check status
vagrant status
```

### 3. Verify Services
```bash
# Check if all VMs are running
vagrant status

# Should show:
# gateway-vm     running (virtualbox)
# inventory-vm   running (virtualbox) 
# billing-vm     running (virtualbox)
```

### 4. Test Basic Connectivity
```bash
# Test API Gateway
curl http://*************:3000/api/movies

# Should return: [] (empty array initially)
```

## API Documentation

### Base URLs
- **API Gateway**: `http://*************:3000`
- **Direct Inventory Access**: `http://*************:8080` (not recommended)

### Movie Inventory API

#### Get All Movies
```http
GET /api/movies
```
**Response**: Array of movie objects

#### Search Movies by Title
```http
GET /api/movies?title=avengers
```
**Response**: Filtered array of movies

#### Get Specific Movie
```http
GET /api/movies/{id}
```
**Response**: Single movie object or 404

#### Create New Movie
```http
POST /api/movies
Content-Type: application/json

{
  "title": "The Matrix",
  "description": "A computer programmer discovers reality is a simulation"
}
```
**Response**: Created movie object with ID

#### Update Movie
```http
PUT /api/movies/{id}
Content-Type: application/json

{
  "title": "The Matrix Reloaded",
  "description": "Neo continues his fight against the machines"
}
```
**Response**: Updated movie object

#### Delete Specific Movie
```http
DELETE /api/movies/{id}
```
**Response**: 204 No Content

#### Delete All Movies
```http
DELETE /api/movies
```
**Response**: 204 No Content

### Billing API

#### Create Order
```http
POST /api/billing
Content-Type: application/json

{
  "user_id": "user123",
  "number_of_items": "2",
  "total_amount": "29.99"
}
```
**Response**: 
```json
{
  "message": "Order queued successfully"
}
```

## Testing the APIs

### Using cURL

#### Basic Movie Operations
```bash
# Create a movie
curl -X POST http://*************:3000/api/movies \
  -H "Content-Type: application/json" \
  -d '{"title": "Inception", "description": "A thief enters dreams to plant ideas"}'

# Get all movies
curl http://*************:3000/api/movies

# Search movies
curl "http://*************:3000/api/movies?title=inception"

# Update movie (replace {id} with actual ID)
curl -X PUT http://*************:3000/api/movies/1 \
  -H "Content-Type: application/json" \
  -d '{"title": "Inception", "description": "Updated description"}'

# Delete movie
curl -X DELETE http://*************:3000/api/movies/1
```

#### Billing Operations
```bash
# Create an order
curl -X POST http://*************:3000/api/billing \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user123", "number_of_items": "3", "total_amount": "45.99"}'
```

### Using Postman

1. **Import Collection**: Create a new collection in Postman
2. **Set Base URL**: `http://*************:3000`
3. **Add Requests**: Create requests for each endpoint above
4. **Test Scenarios**: 
   - Create multiple movies
   - Search and filter
   - Update existing records
   - Test error cases (invalid IDs, malformed JSON)

## Managing Services

### VM Management
```bash
# Stop all VMs
vagrant halt

# Start all VMs
vagrant up

# Restart specific VM
vagrant reload gateway-vm

# SSH into a VM
vagrant ssh gateway-vm
vagrant ssh inventory-vm
vagrant ssh billing-vm

# Destroy all VMs (careful!)
vagrant destroy
```

### Service Management (Inside VMs)
```bash
# SSH into a VM first
vagrant ssh gateway-vm

# Check PM2 processes
sudo pm2 list

# View logs
sudo pm2 logs gateway
sudo pm2 logs inventory_app
sudo pm2 logs billing_app

# Restart a service
sudo pm2 restart gateway

# Stop a service
sudo pm2 stop gateway

# Start a service
sudo pm2 start gateway
```

### Service Status Commands
```bash
# Check PostgreSQL
sudo systemctl status postgresql

# Check RabbitMQ (billing-vm only)
sudo systemctl status rabbitmq-server

# Check RabbitMQ queues
sudo rabbitmqctl list_queues
```

## Database Access

### PostgreSQL Access

#### Inventory Database (inventory-vm)
```bash
# SSH into inventory VM
vagrant ssh inventory-vm

# Access PostgreSQL
sudo -i -u postgres
psql

# Connect to movies database
\c movies

# List tables
\dt

# View movies
SELECT * FROM movies;

# Exit
\q
exit
```

#### Billing Database (billing-vm)
```bash
# SSH into billing VM
vagrant ssh billing-vm

# Access PostgreSQL
sudo -i -u postgres
psql

# Connect to orders database
\c orders

# View orders
SELECT * FROM orders;
```

### RabbitMQ Management

```bash
# SSH into billing VM
vagrant ssh billing-vm

# Check queue status
sudo rabbitmqctl list_queues

# View queue details
sudo rabbitmqctl list_queues name messages consumers

# Enable management plugin (optional)
sudo rabbitmq-plugins enable rabbitmq_management
# Then access: http://*************:15672 (admin/admin123)
```

## Troubleshooting

### Common Issues

#### Services Not Starting
```bash
# Check PM2 logs
sudo pm2 logs

# Restart all services
sudo pm2 restart all

# Check system resources
free -h
df -h
```

#### Database Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Restart PostgreSQL
sudo systemctl restart postgresql

# Check database exists
sudo -u postgres psql -l
```

#### Network Connectivity Issues
```bash
# Test internal connectivity
ping *************
ping *************
ping *************

# Check if ports are open
netstat -tlnp | grep :3000
netstat -tlnp | grep :8080
```

#### RabbitMQ Issues
```bash
# Check RabbitMQ status
sudo systemctl status rabbitmq-server

# Restart RabbitMQ
sudo systemctl restart rabbitmq-server

# Check users
sudo rabbitmqctl list_users
```

### Log Locations
- **PM2 Logs**: `~/.pm2/logs/`
- **PostgreSQL Logs**: `/var/log/postgresql/`
- **RabbitMQ Logs**: `/var/log/rabbitmq/`
- **System Logs**: `/var/log/syslog`

### Performance Monitoring
```bash
# Check system resources
htop
free -h
df -h

# Monitor PM2 processes
sudo pm2 monit

# Check network connections
netstat -an | grep LISTEN
```

## Development

### Local Development Setup

#### Running Services Locally (Alternative to VMs)
```bash
# Install dependencies for each service
cd srcs/api-gateway
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Set environment variables
export GATEWAY_PORT=3000
export INVENTORY_API_HOST=localhost
export INVENTORY_API_PORT=8080
# ... other variables from .env

# Run the service
python server.py
```

#### Making Code Changes
1. **Edit Source Code**: Modify files in `srcs/` directory
2. **Sync Changes**: Vagrant automatically syncs `/vagrant` with host
3. **Restart Services**: Use PM2 commands to restart affected services
4. **Test Changes**: Use API calls to verify functionality

#### Adding New Features
1. **Database Changes**: Update models in `models/` directory
2. **API Endpoints**: Add routes in `routes/` directory
3. **Business Logic**: Implement in `controllers/` directory
4. **Dependencies**: Update `requirements.txt` if needed

### Environment Configuration

The `.env` file contains all configuration:
```env
# API Gateway Configuration
GATEWAY_PORT=3000
GATEWAY_HOST=*************

# Inventory API Configuration  
INVENTORY_API_PORT=8080
INVENTORY_API_HOST=*************
# ... database settings

# Billing API Configuration
BILLING_API_PORT=8081
BILLING_API_HOST=*************
# ... database and RabbitMQ settings
```

### Testing Strategy
1. **Unit Tests**: Test individual functions
2. **Integration Tests**: Test API endpoints
3. **End-to-End Tests**: Test complete workflows
4. **Load Testing**: Test system under load

---

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review PM2 and system logs
3. Verify all prerequisites are installed
4. Ensure VMs have sufficient resources

**System Requirements**:
- RAM: 4GB minimum (3 VMs × 1GB each + host)
- Disk: 10GB free space
- CPU: 2+ cores recommended
