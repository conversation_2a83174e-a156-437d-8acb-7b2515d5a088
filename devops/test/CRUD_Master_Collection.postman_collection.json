{"info": {"_postman_id": "crud-master-collection-001", "name": "CRUD Master Microservices API Collection", "description": "Comprehensive test collection for CRUD Master microservices project including Movie Inventory API and Billing API with complete CRUD operations, error handling, and workflow testing.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "crud-master"}, "item": [{"name": "🎬 Movie Inventory API", "item": [{"name": "Get All Movies (Empty)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is an array\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('array');", "});", "", "pm.test(\"Response time is less than 2000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "console.log(\"Initial movie count:\", pm.response.json().length);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/movies", "host": ["{{base_url}}"], "path": ["api", "movies"]}, "description": "Retrieve all movies from the inventory. Initially should return empty array."}}, {"name": "Create Movie - The Matrix", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Response has required fields\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(responseJson).to.have.property('title');", "    pm.expect(responseJson).to.have.property('description');", "});", "", "pm.test(\"Movie data is correct\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.title).to.eql('The Matrix');", "    pm.expect(responseJson.description).to.eql('A computer programmer discovers reality is a simulation');", "});", "", "pm.test(\"ID is a number\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.id).to.be.a('number');", "    pm.expect(responseJson.id).to.be.greaterThan(0);", "});", "", "// Store movie ID for subsequent tests", "const responseJson = pm.response.json();", "pm.environment.set('movie_id', responseJson.id);", "console.log('Created movie with ID:', responseJson.id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"The Matrix\",\n  \"description\": \"A computer programmer discovers reality is a simulation\"\n}"}, "url": {"raw": "{{base_url}}/api/movies", "host": ["{{base_url}}"], "path": ["api", "movies"]}, "description": "Create a new movie in the inventory with title and description."}}, {"name": "Create Movie - Inception", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Response has required fields\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(responseJson).to.have.property('title');", "    pm.expect(responseJson).to.have.property('description');", "});", "", "pm.test(\"Movie title is correct\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.title).to.eql('Inception');", "});", "", "console.log('Created second movie with ID:', pm.response.json().id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Inception\",\n  \"description\": \"A thief who enters people's dreams to steal secrets is given the task of planting an idea in someone's mind.\"\n}"}, "url": {"raw": "{{base_url}}/api/movies", "host": ["{{base_url}}"], "path": ["api", "movies"]}, "description": "Create another movie to test multiple records."}}, {"name": "Get All Movies (With Data)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is an array with movies\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('array');", "    pm.expect(responseJson.length).to.be.greaterThan(0);", "});", "", "pm.test(\"Movies have required fields\", function () {", "    const responseJson = pm.response.json();", "    responseJson.forEach(movie => {", "        pm.expect(movie).to.have.property('id');", "        pm.expect(movie).to.have.property('title');", "        pm.expect(movie).to.have.property('description');", "    });", "});", "", "console.log('Total movies found:', pm.response.json().length);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/movies", "host": ["{{base_url}}"], "path": ["api", "movies"]}, "description": "Retrieve all movies after creating some test data."}}, {"name": "Get Movie by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has movie data\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(responseJson).to.have.property('title');", "    pm.expect(responseJson).to.have.property('description');", "});", "", "pm.test(\"Movie ID matches requested ID\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.id).to.eql(parseInt(pm.environment.get('movie_id')));", "});", "", "console.log('Retrieved movie:', pm.response.json().title);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/movies/{{movie_id}}", "host": ["{{base_url}}"], "path": ["api", "movies", "{{movie_id}}"]}, "description": "Retrieve a specific movie by its ID."}}, {"name": "Search Movies by Title", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is an array\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('array');", "});", "", "pm.test(\"Search results contain search term\", function () {", "    const responseJson = pm.response.json();", "    if (responseJson.length > 0) {", "        responseJson.forEach(movie => {", "            pm.expect(movie.title.toLowerCase()).to.include('matrix');", "        });", "    }", "});", "", "console.log('Search results count:', pm.response.json().length);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/movies?title=matrix", "host": ["{{base_url}}"], "path": ["api", "movies"], "query": [{"key": "title", "value": "matrix"}]}, "description": "Search for movies containing 'matrix' in the title."}}, {"name": "Update Movie", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has updated data\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(responseJson).to.have.property('title');", "    pm.expect(responseJson).to.have.property('description');", "});", "", "pm.test(\"Movie was updated correctly\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.title).to.include('Reloaded');", "});", "", "console.log('Updated movie:', pm.response.json().title);"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"The Matrix Reloaded\",\n  \"description\": \"<PERSON> and his allies race against time before the machines discover the city of Zion and destroy it.\"\n}"}, "url": {"raw": "{{base_url}}/api/movies/{{movie_id}}", "host": ["{{base_url}}"], "path": ["api", "movies", "{{movie_id}}"]}, "description": "Update an existing movie's title and description."}}, {"name": "Get Movie by Invalid ID (404 Test)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {", "    pm.response.to.have.status(404);", "});", "", "pm.test(\"Response contains error message\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('error');", "    pm.expect(responseJson.error).to.include('not found');", "});", "", "console.log('404 error handled correctly');"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/movies/99999", "host": ["{{base_url}}"], "path": ["api", "movies", "99999"]}, "description": "Test error handling for non-existent movie ID."}}, {"name": "Create Movie with Invalid Data (400 Test)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400 or 500 (error expected)\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([400, 500]);", "});", "", "console.log('Invalid data error handled correctly');"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"invalid_field\": \"test\"\n}"}, "url": {"raw": "{{base_url}}/api/movies", "host": ["{{base_url}}"], "path": ["api", "movies"]}, "description": "Test error handling for invalid movie data."}}], "description": "Movie Inventory API endpoints for managing the movie catalog with full CRUD operations."}, {"name": "💳 Billing API", "item": [{"name": "Create Billing Order", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains success message\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('queued successfully');", "});", "", "pm.test(\"Response time is reasonable\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(3000);", "});", "", "console.log('Billing order created successfully');"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"test_user_123\",\n  \"number_of_items\": \"3\",\n  \"total_amount\": \"45.99\"\n}"}, "url": {"raw": "{{base_url}}/api/billing", "host": ["{{base_url}}"], "path": ["api", "billing"]}, "description": "Create a billing order that will be processed asynchronously via RabbitMQ."}}, {"name": "Create Multiple Billing Orders", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response contains success message\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "});", "", "console.log('Multiple orders test completed');"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"premium_user_456\",\n  \"number_of_items\": \"10\",\n  \"total_amount\": \"199.99\"\n}"}, "url": {"raw": "{{base_url}}/api/billing", "host": ["{{base_url}}"], "path": ["api", "billing"]}, "description": "Create another billing order to test multiple order processing."}}], "description": "Billing API endpoints for processing orders via RabbitMQ messaging."}, {"name": "🗑️ Cleanup Operations", "item": [{"name": "Delete Specific Movie", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Response body is empty\", function () {", "    pm.expect(pm.response.text()).to.be.empty;", "});", "", "console.log('Movie deleted successfully');"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/movies/{{movie_id}}", "host": ["{{base_url}}"], "path": ["api", "movies", "{{movie_id}}"]}, "description": "Delete a specific movie by ID."}}, {"name": "Verify Movie Deleted (404 Test)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {", "    pm.response.to.have.status(404);", "});", "", "console.log('Confirmed movie was deleted');"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/movies/{{movie_id}}", "host": ["{{base_url}}"], "path": ["api", "movies", "{{movie_id}}"]}, "description": "Verify that the deleted movie no longer exists."}}, {"name": "Delete All Movies", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {", "    pm.response.to.have.status(204);", "});", "", "pm.test(\"Response body is empty\", function () {", "    pm.expect(pm.response.text()).to.be.empty;", "});", "", "console.log('All movies deleted successfully');"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/movies", "host": ["{{base_url}}"], "path": ["api", "movies"]}, "description": "Delete all movies from the inventory."}}, {"name": "Verify All Movies Deleted", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is empty array\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('array');", "    pm.expect(responseJson).to.have.lengthOf(0);", "});", "", "console.log('Confirmed all movies were deleted');"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/movies", "host": ["{{base_url}}"], "path": ["api", "movies"]}, "description": "Verify that all movies have been deleted."}}], "description": "Cleanup operations to delete test data and verify deletions."}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set up environment variables if not already set", "if (!pm.environment.get('base_url')) {", "    pm.environment.set('base_url', 'http://localhost:3000');", "    console.log('✅ Set base_url to localhost:3000 (port forwarded)');", "}", "if (!pm.environment.get('base_url_vm')) {", "    pm.environment.set('base_url_vm', 'http://*************:3000');", "    console.log('✅ Set base_url_vm to VM IP address');", "}", "if (!pm.environment.get('gateway_host')) {", "    pm.environment.set('gateway_host', '*************');", "}", "if (!pm.environment.get('gateway_port')) {", "    pm.environment.set('gateway_port', '3000');", "}", "if (!pm.environment.get('inventory_host')) {", "    pm.environment.set('inventory_host', '*************');", "}", "if (!pm.environment.get('inventory_port')) {", "    pm.environment.set('inventory_port', '8080');", "}", "if (!pm.environment.get('billing_host')) {", "    pm.environment.set('billing_host', '*************');", "}", "if (!pm.environment.get('test_user_id')) {", "    pm.environment.set('test_user_id', 'test_user_123');", "}", "if (!pm.environment.get('sample_movie_title')) {", "    pm.environment.set('sample_movie_title', 'The Matrix');", "}", "if (!pm.environment.get('sample_movie_description')) {", "    pm.environment.set('sample_movie_description', 'A computer programmer discovers reality is a simulation');", "}", "", "console.log('🚀 Starting CRUD Master API Test Suite');", "console.log('Base URL (localhost):', pm.environment.get('base_url'));", "console.log('Base URL (VM IP):', pm.environment.get('base_url_vm'));", "console.log('Environment variables auto-configured ✅');"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test to log response details", "console.log('Response Status:', pm.response.status);", "console.log('Response Time:', pm.response.responseTime + 'ms');"]}}]}