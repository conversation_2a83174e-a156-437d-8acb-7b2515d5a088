# CRUD Master - Postman Collection

## 📋 Overview

This comprehensive Postman collection provides complete API testing coverage for the CRUD Master microservices project. It includes 17 carefully crafted requests with automated test scripts, environment variables, and error handling scenarios.

## 📁 Files Included

| File | Description |
|------|-------------|
| `CRUD_Master_Collection.postman_collection.json` | Main test collection with 17 API requests |
| `CRUD_Master_Environment.postman_environment.json` | Environment variables and configuration |
| `POSTMAN_TESTING_GUIDE.md` | Detailed testing instructions |
| `POSTMAN_COLLECTION_README.md` | This overview document |

## 🚀 Quick Start

### 1. Import into Postman
```
1. Open Postman
2. Click "Import" → Select both JSON files
3. Set environment to "CRUD Master Environment"
4. Ready to test!
```

### 2. Run Complete Test Suite
```
1. Click on collection name
2. Click "Run" button
3. Select all requests
4. Click "Run CRUD Master..."
5. View comprehensive test results
```

## 🧪 Test Coverage

### Movie Inventory API (11 tests)
- ✅ **CRUD Operations**: Create, Read, Update, Delete movies
- ✅ **Search Functionality**: Filter movies by title
- ✅ **Error Handling**: 404 and 400 error scenarios
- ✅ **Data Validation**: Response structure and type checking
- ✅ **Workflow Testing**: Complete movie lifecycle

### Billing API (2 tests)
- ✅ **Order Processing**: Create billing orders via RabbitMQ
- ✅ **Multiple Orders**: Test concurrent order handling
- ✅ **Async Validation**: Verify message queue processing

### Cleanup Operations (4 tests)
- ✅ **Selective Deletion**: Remove specific movies
- ✅ **Bulk Deletion**: Clear all test data
- ✅ **Verification**: Confirm deletions completed
- ✅ **State Reset**: Return system to clean state

## 📊 Expected Results

### Successful Test Run
- **Total Requests**: 17
- **Test Assertions**: 60+ automated checks
- **Pass Rate**: 100% (when system is healthy)
- **Average Response Time**: < 2000ms
- **Error Tests**: Properly validate 404/400 responses

### Key Validations
- HTTP status codes (200, 201, 204, 404)
- JSON response structure
- Data type verification
- Required field presence
- Error message content
- Response time limits
- Database state consistency

## 🔧 Environment Variables

Pre-configured variables for seamless testing:

```json
{
  "base_url": "http://*************:3000",
  "movie_id": "(dynamically set)",
  "test_user_id": "test_user_123",
  "sample_movie_title": "The Matrix",
  "sample_movie_description": "A computer programmer discovers reality is a simulation"
}
```

## 📝 Sample Test Data

### Movie Creation
```json
{
  "title": "The Matrix",
  "description": "A computer programmer discovers reality is a simulation"
}
```

### Billing Order
```json
{
  "user_id": "test_user_123",
  "number_of_items": "3",
  "total_amount": "45.99"
}
```

## 🔍 Test Scripts Features

### Automated Assertions
- Status code validation
- Response structure checking
- Data type verification
- Error message validation
- Performance monitoring

### Dynamic Variables
- Auto-capture movie IDs from creation responses
- Environment variable updates
- Cross-request data sharing

### Logging & Debugging
- Console output for test progress
- Response time tracking
- Error details capture

## 🛠️ Troubleshooting

### Before Running Tests
1. Ensure VMs are running: `vagrant status`
2. Verify services are online: `vagrant ssh gateway-vm -c "sudo pm2 list"`
3. Test basic connectivity: `curl http://*************:3000/api/movies`

### Common Issues
- **Connection Refused**: VMs not started or services down
- **500 Errors**: Database connection issues
- **Timeout**: Services still starting up (wait 30 seconds)
- **Test Failures**: Previous test data interference

### Quick Fixes
```bash
# Restart all services
vagrant ssh gateway-vm -c "sudo pm2 restart all"
vagrant ssh inventory-vm -c "sudo pm2 restart all"
vagrant ssh billing-vm -c "sudo pm2 restart all"

# Check service logs
vagrant ssh inventory-vm -c "sudo pm2 logs inventory_app --lines 10"
```

## 🎯 Usage Scenarios

### Development Testing
- Run individual requests during development
- Validate API changes
- Test error handling improvements

### Integration Testing
- Run complete collection for full system validation
- Verify microservices communication
- Test database persistence

### Regression Testing
- Automated testing after code changes
- CI/CD pipeline integration
- Performance baseline validation

### Demo & Training
- Showcase API functionality
- Demonstrate microservices architecture
- Training new team members

## 🔄 Test Execution Order

The collection is designed to run in sequence:

1. **Setup Phase**: Get initial state (empty movies)
2. **Creation Phase**: Create test movies
3. **Validation Phase**: Verify created data
4. **Operation Phase**: Test CRUD operations
5. **Error Phase**: Test error scenarios
6. **Billing Phase**: Test order processing
7. **Cleanup Phase**: Remove test data

## 📈 Advanced Features

### Newman CLI Integration
```bash
newman run CRUD_Master_Collection.postman_collection.json \
  -e CRUD_Master_Environment.postman_environment.json \
  --reporters cli,json,html
```

### Custom Environments
Create additional environments for:
- Local development
- Staging systems
- Performance testing
- Different configurations

### Extended Testing
- Add load testing scenarios
- Implement data-driven testing
- Create custom validation scripts

## 📚 Documentation Links

- **Main Project**: `README.md`
- **User Guide**: `USER_GUIDE.md`
- **Testing Guide**: `POSTMAN_TESTING_GUIDE.md`
- **API Documentation**: See User Guide API section

## ✅ Validation Checklist

Before using this collection:
- [ ] CRUD Master system is deployed and running
- [ ] All 3 VMs are in "running" state
- [ ] API Gateway responds to basic requests
- [ ] Postman environment is imported and active
- [ ] Collection is imported successfully

## 🎉 Success Indicators

A successful test run will show:
- All 17 requests complete successfully
- 60+ test assertions pass
- No connection errors
- Proper error handling for invalid scenarios
- Clean state after cleanup operations

---

**Collection Version**: 1.0  
**Created**: 2024-12-29  
**System Compatibility**: CRUD Master v1.0  
**Postman Version**: 10.0+
