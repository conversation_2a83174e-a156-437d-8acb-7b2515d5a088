{"info": {"_postman_id": "crud-master-collection-fixed-001", "name": "CRUD Master Microservices API Collection (Fixed)", "description": "Fixed version of the comprehensive test collection for CRUD Master microservices project with proper environment variable handling.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "crud-master"}, "item": [{"name": "🎬 Movie Inventory API", "item": [{"name": "Get All Movies (Empty)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is an array\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.be.an('array');", "});", "", "pm.test(\"Response time is less than 2000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "console.log(\"Initial movie count:\", pm.response.json().length);"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/movies", "host": ["{{base_url}}"], "path": ["api", "movies"]}, "description": "Retrieve all movies from the inventory. Initially should return empty array."}}, {"name": "Create Movie - The Matrix", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Response has required fields\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(responseJson).to.have.property('title');", "    pm.expect(responseJson).to.have.property('description');", "});", "", "pm.test(\"Movie data is correct\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.title).to.eql('The Matrix');", "    pm.expect(responseJson.description).to.include('computer programmer');", "});", "", "pm.test(\"ID is a number\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.id).to.be.a('number');", "    pm.expect(responseJson.id).to.be.greaterThan(0);", "});", "", "// Store movie ID for subsequent tests", "const responseJson = pm.response.json();", "pm.environment.set('movie_id', responseJson.id);", "console.log('Created movie with ID:', responseJson.id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"The Matrix\",\n  \"description\": \"A computer programmer discovers reality is a simulation\"\n}"}, "url": {"raw": "{{base_url}}/api/movies", "host": ["{{base_url}}"], "path": ["api", "movies"]}, "description": "Create a new movie in the inventory with title and description."}}, {"name": "Create Movie - Inception", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Response has required fields\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('id');", "    pm.expect(responseJson).to.have.property('title');", "    pm.expect(responseJson).to.have.property('description');", "});", "", "pm.test(\"Movie title is correct\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.title).to.eql('Inception');", "});", "", "console.log('Created second movie with ID:', pm.response.json().id);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Inception\",\n  \"description\": \"A thief who enters people's dreams to steal secrets is given the task of planting an idea in someone's mind.\"\n}"}, "url": {"raw": "{{base_url}}/api/movies", "host": ["{{base_url}}"], "path": ["api", "movies"]}, "description": "Create another movie to test multiple records."}}], "description": "Movie Inventory API endpoints for managing the movie catalog with full CRUD operations."}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["console.log('🚀 Starting CRUD Master API Test Suite');", "console.log('Base URL:', pm.environment.get('base_url'));"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test to log response details", "console.log('Response Status:', pm.response.status);", "console.log('Response Time:', pm.response.responseTime + 'ms');"]}}]}