from flask import Blueprint
from app.controllers import movie_controller

movie_bp = Blueprint('movies', __name__)

# Routes
movie_bp.route('/', methods=['GET'])(movie_controller.get_all_movies)
movie_bp.route('/<int:movie_id>', methods=['GET'])(movie_controller.get_movie_by_id)
movie_bp.route('/', methods=['POST'])(movie_controller.create_movie)
movie_bp.route('/<int:movie_id>', methods=['PUT'])(movie_controller.update_movie)
movie_bp.route('/', methods=['DELETE'])(movie_controller.delete_all_movies)
movie_bp.route('/<int:movie_id>', methods=['DELETE'])(movie_controller.delete_movie)
