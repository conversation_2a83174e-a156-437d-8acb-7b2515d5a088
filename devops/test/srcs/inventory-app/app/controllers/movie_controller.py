from flask import request, jsonify
from app.config.database import db
from app.models.movie import Movie

def get_all_movies():
    try:
        title_filter = request.args.get('title')
        
        if title_filter:
            movies = Movie.query.filter(
                Movie.title.ilike(f'%{title_filter}%')
            ).all()
        else:
            movies = Movie.query.all()
        
        return jsonify([movie.to_dict() for movie in movies]), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def get_movie_by_id(movie_id):
    try:
        movie = Movie.query.get(movie_id)
        if not movie:
            return jsonify({'error': 'Movie not found'}), 404
        return jsonify(movie.to_dict()), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def create_movie():
    try:
        data = request.get_json()
        movie = Movie(
            title=data.get('title'),
            description=data.get('description')
        )
        db.session.add(movie)
        db.session.commit()
        return jsonify(movie.to_dict()), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def update_movie(movie_id):
    try:
        movie = Movie.query.get(movie_id)
        if not movie:
            return jsonify({'error': 'Movie not found'}), 404
        
        data = request.get_json()
        if 'title' in data:
            movie.title = data['title']
        if 'description' in data:
            movie.description = data['description']
        
        db.session.commit()
        return jsonify(movie.to_dict()), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def delete_movie(movie_id):
    try:
        movie = Movie.query.get(movie_id)
        if not movie:
            return jsonify({'error': 'Movie not found'}), 404
        
        db.session.delete(movie)
        db.session.commit()
        return '', 204
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def delete_all_movies():
    try:
        Movie.query.delete()
        db.session.commit()
        return '', 204
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
