import os
import requests
from flask import request, Response

def setup_proxy(app):
    inventory_host = os.environ.get('INVENTORY_API_HOST', 'localhost')
    inventory_port = os.environ.get('INVENTORY_API_PORT', '8080')
    inventory_url = f'http://{inventory_host}:{inventory_port}'
    
    @app.route('/api/movies', methods=['GET', 'POST', 'DELETE'])
    @app.route('/api/movies/<path:path>', methods=['GET', 'PUT', 'DELETE'])
    def proxy_inventory(path=''):
        # Build the target URL
        if path:
            target_url = f'{inventory_url}/api/movies/{path}'
        else:
            target_url = f'{inventory_url}/api/movies'
        
        # Add query parameters if any
        if request.query_string:
            target_url += f'?{request.query_string.decode()}'
        
        # Forward the request
        try:
            if request.method == 'GET':
                resp = requests.get(target_url)
            elif request.method == 'POST':
                resp = requests.post(target_url, json=request.get_json())
            elif request.method == 'PUT':
                resp = requests.put(target_url, json=request.get_json())
            elif request.method == 'DELETE':
                resp = requests.delete(target_url)
            
            # Return the response
            return Response(
                resp.content,
                status=resp.status_code,
                headers=dict(resp.headers)
            )
        except requests.exceptions.RequestException as e:
            return {'error': str(e)}, 500
