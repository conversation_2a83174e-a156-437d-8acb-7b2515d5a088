import os
import json
import pika
from flask import request, jsonify

def get_rabbitmq_connection():
    credentials = pika.PlainCredentials(
        os.environ.get('RABBITMQ_USER', 'guest'),
        os.environ.get('RABBITMQ_PASSWORD', 'guest')
    )
    parameters = pika.ConnectionParameters(
        host=os.environ.get('RABBITMQ_HOST', 'localhost'),
        port=int(os.environ.get('RABBITMQ_PORT', 5672)),
        credentials=credentials
    )
    return pika.BlockingConnection(parameters)

def setup_billing_routes(app):
    @app.route('/api/billing', methods=['POST'])
    def billing():
        try:
            # Get request data
            data = request.get_json()
            
            # Connect to RabbitMQ
            connection = get_rabbitmq_connection()
            channel = connection.channel()
            
            # Declare queue
            queue_name = os.environ.get('RABBITMQ_QUEUE', 'billing_queue')
            channel.queue_declare(queue=queue_name, durable=True)
            
            # Send message
            message = json.dumps(data)
            channel.basic_publish(
                exchange='',
                routing_key=queue_name,
                body=message,
                properties=pika.BasicProperties(
                    delivery_mode=2,  # Make message persistent
                )
            )
            
            connection.close()
            
            return jsonify({'message': 'Order queued successfully'}), 200
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
