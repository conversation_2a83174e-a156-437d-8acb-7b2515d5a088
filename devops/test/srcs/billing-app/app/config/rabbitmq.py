import os
import json
import pika
import time
from app.controllers.order_controller import create_order

def get_connection():
    while True:
        try:
            credentials = pika.PlainCredentials(
                os.environ.get('RABBITMQ_USER', 'guest'),
                os.environ.get('RABBITMQ_PASSWORD', 'guest')
            )
            parameters = pika.ConnectionParameters(
                host=os.environ.get('RABBITMQ_HOST', 'localhost'),
                port=int(os.environ.get('RABBITMQ_PORT', 5672)),
                credentials=credentials
            )
            return pika.BlockingConnection(parameters)
        except Exception as e:
            print(f"Failed to connect to RabbitMQ: {e}")
            time.sleep(5)

def callback(ch, method, properties, body):
    try:
        # Parse message
        data = json.loads(body.decode())
        print(f"Received message: {data}")
        
        # Create order
        create_order(data)
        
        # Acknowledge message
        ch.basic_ack(delivery_tag=method.delivery_tag)
        print("Message processed successfully")
        
    except Exception as e:
        print(f"Error processing message: {e}")
        # Requeue message
        ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)

def start_consumer():
    connection = get_connection()
    channel = connection.channel()
    
    queue_name = os.environ.get('RABBITMQ_QUEUE', 'billing_queue')
    channel.queue_declare(queue=queue_name, durable=True)
    
    channel.basic_qos(prefetch_count=1)
    channel.basic_consume(queue=queue_name, on_message_callback=callback)
    
    print(f"Waiting for messages in {queue_name}")
    try:
        channel.start_consuming()
    except KeyboardInterrupt:
        print("Stopping consumer...")
        channel.stop_consuming()
        connection.close()
