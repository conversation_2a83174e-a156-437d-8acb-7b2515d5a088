from app.config.database import db

class Order(db.Model):
    __tablename__ = 'orders'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(50), nullable=False)
    number_of_items = db.Column(db.String(50), nullable=False)
    total_amount = db.Column(db.String(50), nullable=False)
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'number_of_items': self.number_of_items,
            'total_amount': self.total_amount
        }
