from app.config.database import db, create_app
from app.models.order import Order

def create_order(order_data):
    app = create_app()
    with app.app_context():
        try:
            order = Order(
                user_id=order_data.get('user_id'),
                number_of_items=order_data.get('number_of_items'),
                total_amount=order_data.get('total_amount')
            )
            db.session.add(order)
            db.session.commit()
            print(f"Order created: {order.to_dict()}")
            return order
        except Exception as e:
            db.session.rollback()
            print(f"Error creating order: {e}")
            raise e
