#!/bin/bash

# Update system
sudo apt-get update -y
sudo apt-get upgrade -y

# Install PostgreSQL
sudo apt-get install -y postgresql postgresql-contrib

# Install Python3 and pip
sudo apt-get install -y python3 python3-pip python3-venv

# Install PM2
curl -sL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs
sudo npm install pm2 -g

# Setup PostgreSQL
sudo -u postgres psql << EOF
CREATE USER $INVENTORY_DB_USER WITH PASSWORD '$INVENTORY_DB_PASSWORD';
CREATE DATABASE $INVENTORY_DB_NAME OWNER $INVENTORY_DB_USER;
GRANT ALL PRIVILEGES ON DATABASE $INVENTORY_DB_NAME TO $INVENTORY_DB_USER;
EOF

# Create virtual environment and install dependencies
cd /vagrant/srcs/inventory-app
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'inventory_app',
    script: 'venv/bin/python',
    args: 'server.py',
    cwd: '/vagrant/srcs/inventory-app',
    env: {
      PORT: '$INVENTORY_API_PORT',
      DB_NAME: '$INVENTORY_DB_NAME',
      DB_USER: '$INVENTORY_DB_USER',
      DB_PASSWORD: '$INVENTORY_DB_PASSWORD',
      DB_HOST: '$INVENTORY_DB_HOST'
    }
  }]
};
EOF

# Start inventory app with PM2
sudo pm2 start ecosystem.config.js
sudo pm2 save
sudo pm2 startup systemd -u vagrant --hp /home/<USER>
