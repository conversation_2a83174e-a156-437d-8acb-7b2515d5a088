#!/bin/bash

# Update system
sudo apt-get update -y
sudo apt-get upgrade -y

# Install PostgreSQL
sudo apt-get install -y postgresql postgresql-contrib

# Install RabbitMQ
sudo apt-get install -y rabbitmq-server

# Install Python3 and pip
sudo apt-get install -y python3 python3-pip python3-venv

# Install PM2
curl -sL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs
sudo npm install pm2 -g

# Setup PostgreSQL
sudo -u postgres psql << EOF
CREATE USER $BILLING_DB_USER WITH PASSWORD '$BILLING_DB_PASSWORD';
CREATE DATABASE $BILLING_DB_NAME OWNER $BILLING_DB_USER;
GRANT ALL PRIVILEGES ON DATABASE $BILLING_DB_NAME TO $BILLING_DB_USER;
EOF

# Configure RabbitMQ
sudo systemctl start rabbitmq-server
sudo systemctl enable rabbitmq-server
sudo rabbitmqctl add_user $RABBITMQ_USER $RABBITMQ_PASSWORD
sudo rabbitmqctl set_permissions -p / $RABBITMQ_USER ".*" ".*" ".*"
sudo rabbitmqctl set_user_tags $RABBITMQ_USER administrator

# Create virtual environment and install dependencies
cd /vagrant/srcs/billing-app
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'billing_app',
    script: 'venv/bin/python',
    args: 'server.py',
    cwd: '/vagrant/srcs/billing-app',
    env: {
      PORT: '$BILLING_API_PORT',
      DB_NAME: '$BILLING_DB_NAME',
      DB_USER: '$BILLING_DB_USER',
      DB_PASSWORD: '$BILLING_DB_PASSWORD',
      DB_HOST: '$BILLING_DB_HOST',
      RABBITMQ_HOST: 'localhost',
      RABBITMQ_USER: '$RABBITMQ_USER',
      RABBITMQ_PASSWORD: '$RABBITMQ_PASSWORD',
      RABBITMQ_QUEUE: '$RABBITMQ_QUEUE'
    }
  }]
};
EOF

# Start billing app with PM2
sudo pm2 start ecosystem.config.js
sudo pm2 save
sudo pm2 startup systemd -u vagrant --hp /home/<USER>
