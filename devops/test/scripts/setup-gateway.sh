#!/bin/bash

# Update system
sudo apt-get update -y
sudo apt-get upgrade -y

# Install Python3 and pip
sudo apt-get install -y python3 python3-pip python3-venv

# Install PM2
curl -sL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs
sudo npm install pm2 -g

# Create virtual environment and install dependencies
cd /vagrant/srcs/api-gateway
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'gateway',
    script: 'venv/bin/python',
    args: 'server.py',
    cwd: '/vagrant/srcs/api-gateway',
    env: {
      GATEWAY_PORT: '$GATEWAY_PORT',
      INVENTORY_API_HOST: '$INVENTORY_API_HOST',
      INVENTORY_API_PORT: '$INVENTORY_API_PORT',
      RABBITMQ_HOST: '$RABBITMQ_HOST',
      RABBITMQ_PORT: '$RABBITMQ_PORT',
      RABBITMQ_USER: '$RABBITMQ_USER',
      RABBITMQ_PASSWORD: '$RABBITMQ_PASSWORD',
      RABBITMQ_QUEUE: '$RABBITMQ_QUEUE'
    }
  }]
};
EOF

# Start gateway with PM2
sudo pm2 start ecosystem.config.js
sudo pm2 save
sudo pm2 startup systemd -u vagrant --hp /home/<USER>
