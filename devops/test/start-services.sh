#!/bin/bash

# CRUD Master Services Startup Script
# This script starts all microservices after vagrant up

echo "🚀 Starting CRUD Master Microservices..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if VMs are running
print_status "Checking VM status..."
VM_STATUS=$(vagrant status | grep -E "(gateway-vm|inventory-vm|billing-vm)" | grep -v "running")
if [ ! -z "$VM_STATUS" ]; then
    print_error "Some VMs are not running. Please run 'vagrant up' first."
    exit 1
fi
print_success "All VMs are running"

# Fix PostgreSQL authentication
print_status "Fixing PostgreSQL authentication..."
vagrant ssh inventory-vm -c "sudo -u postgres psql -c \"ALTER USER postgres PASSWORD 'postgres123';\"" > /dev/null 2>&1
vagrant ssh billing-vm -c "sudo -u postgres psql -c \"ALTER USER postgres PASSWORD 'postgres123';\"" > /dev/null 2>&1
print_success "PostgreSQL authentication configured"

# Start Gateway Service
print_status "Starting API Gateway service..."
vagrant ssh gateway-vm -c "cd /vagrant/srcs/api-gateway && sudo pm2 start ecosystem.config.js" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_success "API Gateway started successfully"
else
    print_error "Failed to start API Gateway"
fi

# Start Inventory Service
print_status "Starting Inventory service..."
vagrant ssh inventory-vm -c "cd /vagrant/srcs/inventory-app && sudo pm2 start ecosystem.config.js" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_success "Inventory service started successfully"
else
    print_error "Failed to start Inventory service"
fi

# Start Billing Service
print_status "Starting Billing service..."
vagrant ssh billing-vm -c "cd /vagrant/srcs/billing-app && sudo pm2 start ecosystem.config.js" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_success "Billing service started successfully"
else
    print_error "Failed to start Billing service"
fi

# Wait a moment for services to fully start
print_status "Waiting for services to initialize..."
sleep 5

# Test API connectivity
print_status "Testing API connectivity..."
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/movies 2>/dev/null)
if [ "$RESPONSE" = "200" ]; then
    print_success "API Gateway is responding correctly"
    print_success "✅ All services are running and accessible!"
    echo ""
    echo -e "${GREEN}🎉 CRUD Master is ready for testing!${NC}"
    echo ""
    echo "Available endpoints:"
    echo "  • API Gateway (localhost): http://localhost:3000"
    echo "  • API Gateway (VM IP):     http://*************:3000"
    echo "  • Movies API:              http://localhost:3000/api/movies"
    echo "  • Billing API:             http://localhost:3000/api/billing"
    echo ""
    echo "Next steps:"
    echo "  1. Import Postman collection: CRUD_Master_Collection.postman_collection.json"
    echo "  2. Import Postman environment: CRUD_Master_Environment.postman_environment.json"
    echo "  3. Run the test collection in Postman"
    echo ""
else
    print_error "API Gateway is not responding (HTTP $RESPONSE)"
    print_warning "Services may still be starting up. Wait 30 seconds and try again."
fi

# Show service status
echo ""
print_status "Service Status Summary:"
echo "Gateway VM:"
vagrant ssh gateway-vm -c "sudo pm2 list" 2>/dev/null | grep -E "(name|gateway)"
echo ""
echo "Inventory VM:"
vagrant ssh inventory-vm -c "sudo pm2 list" 2>/dev/null | grep -E "(name|inventory)"
echo ""
echo "Billing VM:"
vagrant ssh billing-vm -c "sudo pm2 list" 2>/dev/null | grep -E "(name|billing)"
echo ""

print_status "Startup script completed!"
