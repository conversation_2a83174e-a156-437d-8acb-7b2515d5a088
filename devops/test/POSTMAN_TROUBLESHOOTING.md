# Postman Collection Troubleshooting Guide

## Common Issue: Environment Variable Resolution

### Problem
You're seeing test failures like:
```
failed
Movie data is correct | AssertionError: expected '{{sample_movie_title}}' to deeply equal undefined
```

### Root Cause
This happens when environment variables aren't properly resolved in test scripts. The test is comparing the literal string `'{{sample_movie_title}}'` instead of the actual environment variable value.

### Solutions

#### Solution 1: Use Fixed Collection (Recommended)
Use the `CRUD_Master_Collection_Fixed.postman_collection.json` file which has hardcoded values instead of environment variables in test assertions.

#### Solution 2: Fix Environment Variable Access
If you want to use environment variables in tests, the correct syntax is:

**❌ Wrong (in test scripts):**
```javascript
pm.expect(responseJson.title).to.eql('{{sample_movie_title}}');
```

**✅ Correct (in test scripts):**
```javascript
pm.expect(responseJson.title).to.eql(pm.environment.get('sample_movie_title'));
```

#### Solution 3: Manual Fix
If you're using the original collection, manually edit the test script:

1. Open the "Create Movie - The Matrix" request
2. Go to the "Tests" tab
3. Replace this line:
   ```javascript
   pm.expect(responseJson.title).to.eql('{{sample_movie_title}}');
   ```
   With:
   ```javascript
   pm.expect(responseJson.title).to.eql('The Matrix');
   ```

### Environment Variable Usage Rules

#### ✅ Works in Request Bodies:
```json
{
  "title": "{{sample_movie_title}}",
  "description": "{{sample_movie_description}}"
}
```

#### ✅ Works in URLs:
```
{{base_url}}/api/movies/{{movie_id}}
```

#### ✅ Works in Test Scripts (with proper syntax):
```javascript
pm.environment.get('variable_name')
```

#### ❌ Doesn't Work in Test Scripts:
```javascript
'{{variable_name}}'  // This is just a string literal
```

## Other Common Issues

### Issue: Connection Refused
**Error**: `Error: connect ECONNREFUSED *************:3000`

**Solutions**:
1. Check VMs are running: `vagrant status`
2. Verify services: `vagrant ssh gateway-vm -c "sudo pm2 list"`
3. Wait 30 seconds after `vagrant up` for services to fully start

### Issue: 500 Internal Server Error
**Solutions**:
1. Check service logs: `vagrant ssh inventory-vm -c "sudo pm2 logs inventory_app"`
2. Restart services: `vagrant ssh inventory-vm -c "sudo pm2 restart inventory_app"`
3. Check database connection

### Issue: Environment Not Selected
**Error**: Variables showing as `{{variable_name}}` in requests

**Solution**:
1. Click environment dropdown (top right in Postman)
2. Select "CRUD Master Environment"
3. Verify variables are populated in environment tab

### Issue: Tests Failing Due to Previous Data
**Solution**:
1. Run the "Delete All Movies" request first
2. Or restart the inventory service to reset database state

## Quick Validation Steps

### 1. Test Environment Setup
```bash
# Verify system is running
vagrant status

# Test basic connectivity
curl http://*************:3000/api/movies
```

### 2. Check Postman Environment
1. Environment dropdown shows "CRUD Master Environment"
2. Variables tab shows populated values
3. `base_url` = `http://*************:3000`

### 3. Test Individual Request
1. Select "Get All Movies (Empty)" request
2. Click "Send"
3. Should return `200 OK` with empty array `[]`

## Best Practices

### 1. Use Hardcoded Values for Assertions
Instead of environment variables in test assertions, use actual expected values:
```javascript
pm.expect(responseJson.title).to.eql('The Matrix');
```

### 2. Use Environment Variables for Dynamic Data
Use `pm.environment.set()` and `pm.environment.get()` for data that changes between requests:
```javascript
// Store movie ID from creation response
pm.environment.set('movie_id', responseJson.id);

// Use stored ID in subsequent requests
const movieId = pm.environment.get('movie_id');
```

### 3. Validate Environment Before Tests
Add pre-request scripts to validate environment:
```javascript
if (!pm.environment.get('base_url')) {
    throw new Error('base_url environment variable not set');
}
```

## File Recommendations

### For Immediate Use
- Use `CRUD_Master_Collection_Fixed.postman_collection.json`
- Use `CRUD_Master_Environment.postman_environment.json`

### For Development
- Modify the original collection with proper environment variable syntax
- Add more robust error handling in test scripts

## Support

If you continue to experience issues:

1. **Check System Health**:
   ```bash
   vagrant ssh gateway-vm -c "sudo pm2 list"
   curl http://*************:3000/api/movies
   ```

2. **Verify Postman Setup**:
   - Environment is selected and active
   - Collection imported successfully
   - No syntax errors in test scripts

3. **Use Console Debugging**:
   - Open Postman Console (View → Show Postman Console)
   - Run requests and check console output
   - Look for variable resolution issues

4. **Test Manually First**:
   - Run individual requests without tests
   - Verify API responses are correct
   - Then add test scripts gradually
