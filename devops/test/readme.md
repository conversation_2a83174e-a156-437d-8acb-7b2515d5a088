# CRUD Master Project Implementation Guide (Python)

## Project Structure

```
crud-master/
├── README.md
├── config.yaml
├── .env
├── .gitignore
├── Vagrantfile
├── scripts/
│   ├── setup-gateway.sh
│   ├── setup-inventory.sh
│   └── setup-billing.sh
└── srcs/
    ├── api-gateway/
    │   ├── app/
    │   │   ├── __init__.py
    │   │   ├── proxy.py
    │   │   └── billing.py
    │   ├── requirements.txt
    │   └── server.py
    ├── inventory-app/
    │   ├── app/
    │   │   ├── __init__.py
    │   │   ├── config/
    │   │   │   ├── __init__.py
    │   │   │   └── database.py
    │   │   ├── controllers/
    │   │   │   ├── __init__.py
    │   │   │   └── movie_controller.py
    │   │   ├── models/
    │   │   │   ├── __init__.py
    │   │   │   └── movie.py
    │   │   └── routes/
    │   │       ├── __init__.py
    │   │       └── movie_routes.py
    │   ├── requirements.txt
    │   └── server.py
    └── billing-app/
        ├── app/
        │   ├── __init__.py
        │   ├── config/
        │   │   ├── __init__.py
        │   │   ├── database.py
        │   │   └── rabbitmq.py
        │   ├── controllers/
        │   │   ├── __init__.py
        │   │   └── order_controller.py
        │   └── models/
        │       ├── __init__.py
        │       └── order.py
        ├── requirements.txt
        └── server.py
```

## 1. Environment Variables (.env)

```env
# API Gateway Configuration
GATEWAY_PORT=3000
GATEWAY_HOST=*************

# Inventory API Configuration
INVENTORY_API_PORT=8080
INVENTORY_API_HOST=*************
INVENTORY_DB_NAME=movies
INVENTORY_DB_USER=postgres
INVENTORY_DB_PASSWORD=postgres123
INVENTORY_DB_HOST=localhost

# Billing API Configuration
BILLING_API_PORT=8081
BILLING_API_HOST=*************
BILLING_DB_NAME=orders
BILLING_DB_USER=postgres
BILLING_DB_PASSWORD=postgres123
BILLING_DB_HOST=localhost

# RabbitMQ Configuration
RABBITMQ_HOST=*************
RABBITMQ_PORT=5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=admin123
RABBITMQ_QUEUE=billing_queue
```

## 2. .gitignore

```
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.vagrant/
*.log
```

## 3. Vagrantfile

```ruby
# -*- mode: ruby -*-
# vi: set ft=ruby :

# Load environment variables from .env file
require 'dotenv'
Dotenv.load

Vagrant.configure("2") do |config|
  config.vm.box = "ubuntu/focal64"

  # Gateway VM
  config.vm.define "gateway-vm" do |gateway|
    gateway.vm.hostname = "gateway-vm"
    gateway.vm.network "private_network", ip: ENV['GATEWAY_HOST']
    
    gateway.vm.provider "virtualbox" do |vb|
      vb.memory = "1024"
      vb.name = "gateway-vm"
    end
    
    gateway.vm.provision "shell", path: "scripts/setup-gateway.sh", env: {
      "GATEWAY_PORT" => ENV['GATEWAY_PORT'],
      "INVENTORY_API_HOST" => ENV['INVENTORY_API_HOST'],
      "INVENTORY_API_PORT" => ENV['INVENTORY_API_PORT'],
      "RABBITMQ_HOST" => ENV['RABBITMQ_HOST'],
      "RABBITMQ_PORT" => ENV['RABBITMQ_PORT'],
      "RABBITMQ_USER" => ENV['RABBITMQ_USER'],
      "RABBITMQ_PASSWORD" => ENV['RABBITMQ_PASSWORD'],
      "RABBITMQ_QUEUE" => ENV['RABBITMQ_QUEUE']
    }
  end

  # Inventory VM
  config.vm.define "inventory-vm" do |inventory|
    inventory.vm.hostname = "inventory-vm"
    inventory.vm.network "private_network", ip: ENV['INVENTORY_API_HOST']
    
    inventory.vm.provider "virtualbox" do |vb|
      vb.memory = "1024"
      vb.name = "inventory-vm"
    end
    
    inventory.vm.provision "shell", path: "scripts/setup-inventory.sh", env: {
      "INVENTORY_API_PORT" => ENV['INVENTORY_API_PORT'],
      "INVENTORY_DB_NAME" => ENV['INVENTORY_DB_NAME'],
      "INVENTORY_DB_USER" => ENV['INVENTORY_DB_USER'],
      "INVENTORY_DB_PASSWORD" => ENV['INVENTORY_DB_PASSWORD'],
      "INVENTORY_DB_HOST" => ENV['INVENTORY_DB_HOST']
    }
  end

  # Billing VM
  config.vm.define "billing-vm" do |billing|
    billing.vm.hostname = "billing-vm"
    billing.vm.network "private_network", ip: ENV['BILLING_API_HOST']
    
    billing.vm.provider "virtualbox" do |vb|
      vb.memory = "1024"
      vb.name = "billing-vm"
    end
    
    billing.vm.provision "shell", path: "scripts/setup-billing.sh", env: {
      "BILLING_API_PORT" => ENV['BILLING_API_PORT'],
      "BILLING_DB_NAME" => ENV['BILLING_DB_NAME'],
      "BILLING_DB_USER" => ENV['BILLING_DB_USER'],
      "BILLING_DB_PASSWORD" => ENV['BILLING_DB_PASSWORD'],
      "BILLING_DB_HOST" => ENV['BILLING_DB_HOST'],
      "RABBITMQ_USER" => ENV['RABBITMQ_USER'],
      "RABBITMQ_PASSWORD" => ENV['RABBITMQ_PASSWORD'],
      "RABBITMQ_QUEUE" => ENV['RABBITMQ_QUEUE']
    }
  end
end
```

## 4. Setup Scripts

### scripts/setup-gateway.sh

```bash
#!/bin/bash

# Update system
sudo apt-get update -y
sudo apt-get upgrade -y

# Install Python3 and pip
sudo apt-get install -y python3 python3-pip python3-venv

# Install PM2
curl -sL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs
sudo npm install pm2 -g

# Create virtual environment and install dependencies
cd /vagrant/srcs/api-gateway
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'gateway',
    script: 'venv/bin/python',
    args: 'server.py',
    cwd: '/vagrant/srcs/api-gateway',
    env: {
      GATEWAY_PORT: '$GATEWAY_PORT',
      INVENTORY_API_HOST: '$INVENTORY_API_HOST',
      INVENTORY_API_PORT: '$INVENTORY_API_PORT',
      RABBITMQ_HOST: '$RABBITMQ_HOST',
      RABBITMQ_PORT: '$RABBITMQ_PORT',
      RABBITMQ_USER: '$RABBITMQ_USER',
      RABBITMQ_PASSWORD: '$RABBITMQ_PASSWORD',
      RABBITMQ_QUEUE: '$RABBITMQ_QUEUE'
    }
  }]
};
EOF

# Start gateway with PM2
sudo pm2 start ecosystem.config.js
sudo pm2 save
sudo pm2 startup systemd -u vagrant --hp /home/<USER>
```

### scripts/setup-inventory.sh

```bash
#!/bin/bash

# Update system
sudo apt-get update -y
sudo apt-get upgrade -y

# Install PostgreSQL
sudo apt-get install -y postgresql postgresql-contrib

# Install Python3 and pip
sudo apt-get install -y python3 python3-pip python3-venv

# Install PM2
curl -sL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs
sudo npm install pm2 -g

# Setup PostgreSQL
sudo -u postgres psql << EOF
CREATE USER $INVENTORY_DB_USER WITH PASSWORD '$INVENTORY_DB_PASSWORD';
CREATE DATABASE $INVENTORY_DB_NAME OWNER $INVENTORY_DB_USER;
GRANT ALL PRIVILEGES ON DATABASE $INVENTORY_DB_NAME TO $INVENTORY_DB_USER;
EOF

# Create virtual environment and install dependencies
cd /vagrant/srcs/inventory-app
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'inventory_app',
    script: 'venv/bin/python',
    args: 'server.py',
    cwd: '/vagrant/srcs/inventory-app',
    env: {
      PORT: '$INVENTORY_API_PORT',
      DB_NAME: '$INVENTORY_DB_NAME',
      DB_USER: '$INVENTORY_DB_USER',
      DB_PASSWORD: '$INVENTORY_DB_PASSWORD',
      DB_HOST: '$INVENTORY_DB_HOST'
    }
  }]
};
EOF

# Start inventory app with PM2
sudo pm2 start ecosystem.config.js
sudo pm2 save
sudo pm2 startup systemd -u vagrant --hp /home/<USER>
```

### scripts/setup-billing.sh

```bash
#!/bin/bash

# Update system
sudo apt-get update -y
sudo apt-get upgrade -y

# Install PostgreSQL
sudo apt-get install -y postgresql postgresql-contrib

# Install RabbitMQ
sudo apt-get install -y rabbitmq-server

# Install Python3 and pip
sudo apt-get install -y python3 python3-pip python3-venv

# Install PM2
curl -sL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs
sudo npm install pm2 -g

# Setup PostgreSQL
sudo -u postgres psql << EOF
CREATE USER $BILLING_DB_USER WITH PASSWORD '$BILLING_DB_PASSWORD';
CREATE DATABASE $BILLING_DB_NAME OWNER $BILLING_DB_USER;
GRANT ALL PRIVILEGES ON DATABASE $BILLING_DB_NAME TO $BILLING_DB_USER;
EOF

# Configure RabbitMQ
sudo systemctl start rabbitmq-server
sudo systemctl enable rabbitmq-server
sudo rabbitmqctl add_user $RABBITMQ_USER $RABBITMQ_PASSWORD
sudo rabbitmqctl set_permissions -p / $RABBITMQ_USER ".*" ".*" ".*"
sudo rabbitmqctl set_user_tags $RABBITMQ_USER administrator

# Create virtual environment and install dependencies
cd /vagrant/srcs/billing-app
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'billing_app',
    script: 'venv/bin/python',
    args: 'server.py',
    cwd: '/vagrant/srcs/billing-app',
    env: {
      PORT: '$BILLING_API_PORT',
      DB_NAME: '$BILLING_DB_NAME',
      DB_USER: '$BILLING_DB_USER',
      DB_PASSWORD: '$BILLING_DB_PASSWORD',
      DB_HOST: '$BILLING_DB_HOST',
      RABBITMQ_HOST: 'localhost',
      RABBITMQ_USER: '$RABBITMQ_USER',
      RABBITMQ_PASSWORD: '$RABBITMQ_PASSWORD',
      RABBITMQ_QUEUE: '$RABBITMQ_QUEUE'
    }
  }]
};
EOF

# Start billing app with PM2
sudo pm2 start ecosystem.config.js
sudo pm2 save
sudo pm2 startup systemd -u vagrant --hp /home/<USER>
```

## 5. API Gateway Implementation

### srcs/api-gateway/requirements.txt

```
Flask==2.3.3
requests==2.31.0
pika==1.3.2
python-dotenv==1.0.0
```

### srcs/api-gateway/server.py

```python
import os
from flask import Flask
from app.proxy import setup_proxy
from app.billing import setup_billing_routes

app = Flask(__name__)

# Setup routes
setup_proxy(app)
setup_billing_routes(app)

if __name__ == '__main__':
    port = int(os.environ.get('GATEWAY_PORT', 3000))
    app.run(host='0.0.0.0', port=port, debug=True)
```

### srcs/api-gateway/app/__init__.py

```python
# Empty file to make app a package
```

### srcs/api-gateway/app/proxy.py

```python
import os
import requests
from flask import request, Response

def setup_proxy(app):
    inventory_host = os.environ.get('INVENTORY_API_HOST', 'localhost')
    inventory_port = os.environ.get('INVENTORY_API_PORT', '8080')
    inventory_url = f'http://{inventory_host}:{inventory_port}'
    
    @app.route('/api/movies', methods=['GET', 'POST', 'DELETE'])
    @app.route('/api/movies/<path:path>', methods=['GET', 'PUT', 'DELETE'])
    def proxy_inventory(path=''):
        # Build the target URL
        if path:
            target_url = f'{inventory_url}/api/movies/{path}'
        else:
            target_url = f'{inventory_url}/api/movies'
        
        # Add query parameters if any
        if request.query_string:
            target_url += f'?{request.query_string.decode()}'
        
        # Forward the request
        try:
            if request.method == 'GET':
                resp = requests.get(target_url)
            elif request.method == 'POST':
                resp = requests.post(target_url, json=request.get_json())
            elif request.method == 'PUT':
                resp = requests.put(target_url, json=request.get_json())
            elif request.method == 'DELETE':
                resp = requests.delete(target_url)
            
            # Return the response
            return Response(
                resp.content,
                status=resp.status_code,
                headers=dict(resp.headers)
            )
        except requests.exceptions.RequestException as e:
            return {'error': str(e)}, 500
```

### srcs/api-gateway/app/billing.py

```python
import os
import json
import pika
from flask import request, jsonify

def get_rabbitmq_connection():
    credentials = pika.PlainCredentials(
        os.environ.get('RABBITMQ_USER', 'guest'),
        os.environ.get('RABBITMQ_PASSWORD', 'guest')
    )
    parameters = pika.ConnectionParameters(
        host=os.environ.get('RABBITMQ_HOST', 'localhost'),
        port=int(os.environ.get('RABBITMQ_PORT', 5672)),
        credentials=credentials
    )
    return pika.BlockingConnection(parameters)

def setup_billing_routes(app):
    @app.route('/api/billing', methods=['POST'])
    def billing():
        try:
            # Get request data
            data = request.get_json()
            
            # Connect to RabbitMQ
            connection = get_rabbitmq_connection()
            channel = connection.channel()
            
            # Declare queue
            queue_name = os.environ.get('RABBITMQ_QUEUE', 'billing_queue')
            channel.queue_declare(queue=queue_name, durable=True)
            
            # Send message
            message = json.dumps(data)
            channel.basic_publish(
                exchange='',
                routing_key=queue_name,
                body=message,
                properties=pika.BasicProperties(
                    delivery_mode=2,  # Make message persistent
                )
            )
            
            connection.close()
            
            return jsonify({'message': 'Order queued successfully'}), 200
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
```

## 6. Inventory API Implementation

### srcs/inventory-app/requirements.txt

```
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
psycopg2-binary==2.9.7
python-dotenv==1.0.0
```

### srcs/inventory-app/server.py

```python
import os
from flask import Flask
from app.config.database import db
from app.routes.movie_routes import movie_bp

app = Flask(__name__)

# Database configuration
app.config['SQLALCHEMY_DATABASE_URI'] = (
    f"postgresql://{os.environ.get('DB_USER')}:"
    f"{os.environ.get('DB_PASSWORD')}@"
    f"{os.environ.get('DB_HOST', 'localhost')}/"
    f"{os.environ.get('DB_NAME')}"
)
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize database
db.init_app(app)

# Register blueprints
app.register_blueprint(movie_bp, url_prefix='/api/movies')

# Create tables
with app.app_context():
    db.create_all()

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=True)
```

### srcs/inventory-app/app/__init__.py

```python
# Empty file to make app a package
```

### srcs/inventory-app/app/config/__init__.py

```python
# Empty file to make config a package
```

### srcs/inventory-app/app/config/database.py

```python
from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()
```

### srcs/inventory-app/app/models/__init__.py

```python
# Empty file to make models a package
```

### srcs/inventory-app/app/models/movie.py

```python
from app.config.database import db

class Movie(db.Model):
    __tablename__ = 'movies'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description
        }
```

### srcs/inventory-app/app/controllers/__init__.py

```python
# Empty file to make controllers a package
```

### srcs/inventory-app/app/controllers/movie_controller.py

```python
from flask import request, jsonify
from app.config.database import db
from app.models.movie import Movie

def get_all_movies():
    try:
        title_filter = request.args.get('title')
        
        if title_filter:
            movies = Movie.query.filter(
                Movie.title.ilike(f'%{title_filter}%')
            ).all()
        else:
            movies = Movie.query.all()
        
        return jsonify([movie.to_dict() for movie in movies]), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def get_movie_by_id(movie_id):
    try:
        movie = Movie.query.get(movie_id)
        if not movie:
            return jsonify({'error': 'Movie not found'}), 404
        return jsonify(movie.to_dict()), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def create_movie():
    try:
        data = request.get_json()
        movie = Movie(
            title=data.get('title'),
            description=data.get('description')
        )
        db.session.add(movie)
        db.session.commit()
        return jsonify(movie.to_dict()), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def update_movie(movie_id):
    try:
        movie = Movie.query.get(movie_id)
        if not movie:
            return jsonify({'error': 'Movie not found'}), 404
        
        data = request.get_json()
        if 'title' in data:
            movie.title = data['title']
        if 'description' in data:
            movie.description = data['description']
        
        db.session.commit()
        return jsonify(movie.to_dict()), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def delete_movie(movie_id):
    try:
        movie = Movie.query.get(movie_id)
        if not movie:
            return jsonify({'error': 'Movie not found'}), 404
        
        db.session.delete(movie)
        db.session.commit()
        return '', 204
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

def delete_all_movies():
    try:
        Movie.query.delete()
        db.session.commit()
        return '', 204
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
```

### srcs/inventory-app/app/routes/__init__.py

```python
# Empty file to make routes a package
```

### srcs/inventory-app/app/routes/movie_routes.py

```python
from flask import Blueprint
from app.controllers import movie_controller

movie_bp = Blueprint('movies', __name__)

# Routes
movie_bp.route('/', methods=['GET'])(movie_controller.get_all_movies)
movie_bp.route('/<int:movie_id>', methods=['GET'])(movie_controller.get_movie_by_id)
movie_bp.route('/', methods=['POST'])(movie_controller.create_movie)
movie_bp.route('/<int:movie_id>', methods=['PUT'])(movie_controller.update_movie)
movie_bp.route('/', methods=['DELETE'])(movie_controller.delete_all_movies)
movie_bp.route('/<int:movie_id>', methods=['DELETE'])(movie_controller.delete_movie)
```

## 7. Billing API Implementation

### srcs/billing-app/requirements.txt

```
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
psycopg2-binary==2.9.7
pika==1.3.2
python-dotenv==1.0.0
```

### srcs/billing-app/server.py

```python
import os
from app.config.database import db, create_app
from app.config.rabbitmq import start_consumer

app = create_app()

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        print("Starting Billing API RabbitMQ consumer...")
        start_consumer()
```

### srcs/billing-app/app/__init__.py

```python
# Empty file to make app a package
```

### srcs/billing-app/app/config/__init__.py

```python
# Empty file to make config a package
```

### srcs/billing-app/app/config/database.py

```python
import os
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

def create_app():
    app = Flask(__name__)
    
    app.config['SQLALCHEMY_DATABASE_URI'] = (
        f"postgresql://{os.environ.get('DB_USER')}:"
        f"{os.environ.get('DB_PASSWORD')}@"
        f"{os.environ.get('DB_HOST', 'localhost')}/"
        f"{os.environ.get('DB_NAME')}"
    )
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db.init_app(app)
    
    return app
```

### srcs/billing-app/app/config/rabbitmq.py

```python
import os
import json
import pika
import time
from app.controllers.order_controller import create_order

def get_connection():
    while True:
        try:
            credentials = pika.PlainCredentials(
                os.environ.get('RABBITMQ_USER', 'guest'),
                os.environ.get('RABBITMQ_PASSWORD', 'guest')
            )
            parameters = pika.ConnectionParameters(
                host=os.environ.get('RABBITMQ_HOST', 'localhost'),
                port=int(os.environ.get('RABBITMQ_PORT', 5672)),
                credentials=credentials
            )
            return pika.BlockingConnection(parameters)
        except Exception as e:
            print(f"Failed to connect to RabbitMQ: {e}")
            time.sleep(5)

def callback(ch, method, properties, body):
    try:
        # Parse message
        data = json.loads(body.decode())
        print(f"Received message: {data}")
        
        # Create order
        create_order(data)
        
        # Acknowledge message
        ch.basic_ack(delivery_tag=method.delivery_tag)
        print("Message processed successfully")
        
    except Exception as e:
        print(f"Error processing message: {e}")
        # Requeue message
        ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)

def start_consumer():
    connection = get_connection()
    channel = connection.channel()
    
    queue_name = os.environ.get('RABBITMQ_QUEUE', 'billing_queue')
    channel.queue_declare(queue=queue_name, durable=True)
    
    channel.basic_qos(prefetch_count=1)
    channel.basic_consume(queue=queue_name, on_message_callback=callback)
    
    print(f"Waiting for messages in {queue_name}")
    try:
        channel.start_consuming()
    except KeyboardInterrupt:
        print("Stopping consumer...")
        channel.stop_consuming()
        connection.close()
```

### srcs/billing-app/app/models/__init__.py

```python
# Empty file to make models a package
```

### srcs/billing-app/app/models/order.py

```python
from app.config.database import db

class Order(db.Model):
    __tablename__ = 'orders'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(50), nullable=False)
    number_of_items = db.Column(db.String(50), nullable=False)
    total_amount = db.Column(db.String(50), nullable=False)
    
    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'number_of_items': self.number_of_items,
            'total_amount': self.total_amount
        }
```

### srcs/billing-app/app/controllers/__init__.py

```python
# Empty file to make controllers a package
```

### srcs/billing-app/app/controllers/order_controller.py

```python
from app.config.database import db, create_app
from app.models.order import Order

def create_order(order_data):
    app = create_app()
    with app.app_context():
        try:
            order = Order(
                user_id=order_data.get('user_id'),
                number_of_items=order_data.get('number_of_items'),
                total_amount=order_data.get('total_amount')
            )
            db.session.add(order)
            db.session.commit()
            print(f"Order created: {order.to_dict()}")
            return order
        except Exception as e:
            db.session.rollback()
            print(f"Error creating order: {e}")
            raise e
```

## 8. README.md

```markdown
# CRUD Master - Microservices Infrastructure

## Overview

CRUD Master is a microservices-based movie streaming platform infrastructure consisting of:
- **API Gateway**: Routes requests to appropriate services
- **Inventory API**: CRUD operations for movie catalog (PostgreSQL)
- **Billing API**: Processes payments via RabbitMQ messaging (PostgreSQL)

## Architecture

- 3 Virtual Machines managed by Vagrant
- HTTP communication for Inventory API
- RabbitMQ messaging for Billing API
- PostgreSQL databases for data persistence
- PM2 for process management
- Python 3 with Flask and SQLAlchemy

## Prerequisites

- VirtualBox (or VMWare)
- Vagrant
- Postman (for testing)

## Technology Stack

- **Language**: Python 3
- **Web Framework**: Flask
- **ORM**: SQLAlchemy
- **Databases**: PostgreSQL
- **Message Queue**: RabbitMQ (with pika)
- **Process Manager**: PM2
- **Virtualization**: Vagrant with VirtualBox

## Quick Start

1. Clone the repository
2. Ensure `.env` file is present with all required configurations
3. Install dotenv for Ruby: `gem install dotenv`
4. Run `vagrant up` to start all VMs
5. Services will be automatically started via PM2

## VM Structure

- **gateway-vm** (*************): API Gateway on port 3000
- **inventory-vm** (*************): Inventory API on port 8080
- **billing-vm** (*************): Billing API with RabbitMQ

## API Endpoints

### Inventory API (via Gateway)
- `GET /api/movies` - Get all movies
- `GET /api/movies?title=[name]` - Search movies by title
- `GET /api/movies/:id` - Get specific movie
- `POST /api/movies` - Create new movie
- `PUT /api/movies/:id` - Update movie
- `DELETE /api/movies` - Delete all movies
- `DELETE /api/movies/:id` - Delete specific movie

### Billing API (via Gateway)
- `POST /api/billing` - Create new order (queued via RabbitMQ)

## Testing

1. Import the Postman collection from `postman_collection.json`
2. Test all endpoints through the API Gateway (http://*************:3000)

## Managing Services

SSH into VMs:
```bash
vagrant ssh <vm-name>
```

PM2 commands:
```bash
sudo pm2 list
sudo pm2 stop <app_name>
sudo pm2 start <app_name>
sudo pm2 logs <app_name>
```

## Database Access

```bash
vagrant ssh <vm-name>
sudo -i -u postgres
psql
\l  # List databases
\c <database_name>  # Connect to database
TABLE <table_name>;  # View table contents
```

## Development

Each service uses Python virtual environments to isolate dependencies:

```bash
cd /vagrant/srcs/<service-name>
source venv/bin/activate
python server.py
```

## Design Decisions

1. **Python/Flask**: Lightweight and easy to understand microservices
2. **SQLAlchemy ORM**: Simplifies database operations and migrations
3. **PM2 for resilience**: Ensures services restart on failure
4. **RabbitMQ for billing**: Provides message durability and decoupling
5. **Environment variables**: Centralized configuration management
6. **Separate VMs**: Simulates real microservices isolation
7. **Virtual environments**: Python best practice for dependency management

## Troubleshooting

- Check PM2 logs: `sudo pm2 logs <app_name>`
- Verify RabbitMQ status: `sudo systemctl status rabbitmq-server`
- Check PostgreSQL: `sudo systemctl status postgresql`
- Ensure all environment variables are set correctly in `.env`
```