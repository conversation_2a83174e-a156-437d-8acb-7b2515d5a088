__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.vagrant/
*.log


# Python virtual environments
venv/
env/
ENV/
.venv/
*.pyc
__pycache__/
*.py[cod]
*$py.class

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vagrant
.vagrant/
*.log

# Database files
*.sqlite
*.sqlite3
*.db

# Logs
logs/
*.log
npm-debug.log*

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Distribution / packaging
dist/
build/
*.egg-info/

# PM2 files
.pm2/
